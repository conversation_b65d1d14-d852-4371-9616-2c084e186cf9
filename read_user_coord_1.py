#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂用户坐标系1操作测试

重要概念说明：
- 坐标系类型编号：0=关节坐标系, 1=直角坐标系, 2=工具坐标系, 3=用户坐标系
- 用户坐标系编号：1=用户坐标系1, 2=用户坐标系2, 等等
- 本程序使用：coord=3（用户坐标系类型）+ userNum=1（用户坐标系1）

测试步骤：
1. 获取用户坐标系1的当前位置参数
2. 移动到用户坐标系1中的位置 (0, 0, 20) - 原点上方20mm
3. 移动到用户坐标系1中的位置 (10, 10, 25) - 相对移动X+10, Y+10, Z+5
4. 返回到用户坐标系1中的位置 (0, 0, 20) - 原点上方20mm
"""

import sys
import os
import time

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def check_coordinate_system_status(socket_fd):
    """检查当前坐标系状态"""
    print("🗺️ 检查坐标系状态...")

    try:
        # 获取当前坐标系
        coord = 0
        result = nrc.get_current_coord(socket_fd, coord)
        if isinstance(result, list) and len(result) > 1:
            coord = result[1]

        coord_desc = {
            0: "关节坐标系",
            1: "直角坐标系",
            2: "工具坐标系",
            3: "用户坐标系"  # 注意：3是用户坐标系类型，具体的用户坐标系编号由userNum指定
        }
        print(f"当前坐标系: {coord} - {coord_desc.get(coord, '未知坐标系')}")

        # 获取当前用户坐标系编号
        user_num = 0
        result = nrc.get_user_coord_number(socket_fd, user_num)
        if isinstance(result, list) and len(result) > 1:
            user_num = result[1]

        print(f"当前用户坐标系编号: {user_num}")

        return coord, user_num

    except Exception as e:
        print(f"检查坐标系状态失败: {e}")
        return None, None

def get_user_coord_parameters(socket_fd, user_num):
    """获取用户坐标系参数"""
    print(f"获取用户坐标系{user_num}参数...")

    pos = nrc.VectorDouble()
    for _ in range(7):
        pos.append(0.0)

    result = nrc.get_user_coord_para(socket_fd, user_num, pos)
    if result == 0:
        return [pos[i] for i in range(6)]  # 返回X,Y,Z,RX,RY,RZ
    else:
        print(f"获取用户坐标系{user_num}参数失败，错误码: {result}")
        return None

def get_current_cartesian_position(socket_fd):
    """获取当前笛卡尔坐标位置"""
    pos = nrc.VectorDouble()
    for _ in range(7):
        pos.append(0.0)
    
    result = nrc.get_current_position(socket_fd, 1, pos)  # 1=笛卡尔坐标
    if result == 0:
        return [pos[i] for i in range(6)]  # 返回X,Y,Z,RX,RY,RZ
    else:
        print(f"获取当前位置失败，错误码: {result}")
        return None

def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待机器人运动完成"""
    print("等待机器人运动完成...")
    start_time = time.time()

    while time.time() - start_time < timeout_seconds:
        try:
            # 检查机器人运行状态
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]

            # 运行状态说明：
            # 0: 停止状态 (运动完成)
            # 1: 运行状态 (正在运动)
            # 2: 可能是减速或准备停止状态
            # 其他: 异常状态

            if running_status == 0:
                print("✅ 机器人运动完成")
                return True
            elif running_status == 1:
                print(f"⏳ 机器人正在运动... ({time.time() - start_time:.1f}s)")
                time.sleep(0.5)  # 等待0.5秒后再次检查
            elif running_status == 2:
                print(f"🔄 机器人减速中... ({time.time() - start_time:.1f}s)")
                time.sleep(0.3)  # 减速状态检查更频繁
            else:
                print(f"⚠️ 机器人状态异常: {running_status}")
                time.sleep(0.5)

        except Exception as e:
            print(f"检查运动状态时发生错误: {e}")
            time.sleep(0.5)

    print(f"❌ 等待运动完成超时 ({timeout_seconds}秒)")
    return False

def move_to_position_with_user_coord(socket_fd, target_pos, user_num, description):
    """使用指定用户坐标系移动到目标位置"""
    print(f"执行移动: {description}")

    try:
        # 获取移动前位置
        initial_pos = get_current_cartesian_position(socket_fd)
        if initial_pos:
            print(f"移动前位置: X={initial_pos[0]:.3f}, Y={initial_pos[1]:.3f}, Z={initial_pos[2]:.3f}")

        # 首先设置当前用户坐标系
        print(f"设置当前用户坐标系为: {user_num}")
        result = nrc.set_user_coord_number(socket_fd, user_num)
        if result != 0:
            print(f"设置用户坐标系失败，错误码: {result}")
            return False

        time.sleep(0.5)  # 等待设置生效

        # 创建移动命令
        move_cmd = nrc.MoveCmd()

        # 设置目标位置
        move_cmd.targetPosType = 1  # 1=笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos_val in target_pos:
            move_cmd.targetPosValue.append(pos_val)

        # 设置运动参数
        move_cmd.coord = 3      # 用户坐标系（3=用户坐标系类型）
        move_cmd.velocity = 70  # 速度60%（提高速度以便观察）
        move_cmd.acc = 40       # 加速度40%
        move_cmd.dec = 40       # 减速度40%
        move_cmd.pl = 0         # 平滑度
        move_cmd.time = 0       # 时间(0表示不限制)
        move_cmd.toolNum = 0    # 工具号
        move_cmd.userNum = user_num  # 用户坐标系编号（1=用户坐标系1）

        print(f"目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
        print(f"目标姿态: RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")

        # 执行直线运动
        print(f"发送移动命令到用户坐标系{user_num}...")
        result = nrc.robot_movel(socket_fd, move_cmd)

        if result != 0:
            print(f"移动命令发送失败，错误码: {result}")
            return False

        print("移动命令已发送，开始监控运动状态...")

        # 等待运动完成（使用状态检查而不是固定时间）
        if not wait_for_motion_complete(socket_fd, timeout_seconds=30):
            print("❌ 运动未在预期时间内完成")
            return False

        # 验证最终位置
        print("验证最终位置...")
        time.sleep(0.5)  # 等待稳定
        final_pos = get_current_cartesian_position(socket_fd)

        if final_pos:
            print(f"最终位置: X={final_pos[0]:.3f}, Y={final_pos[1]:.3f}, Z={final_pos[2]:.3f}")
            print(f"最终姿态: RX={final_pos[3]:.3f}, RY={final_pos[4]:.3f}, RZ={final_pos[5]:.3f}")

            # 计算实际移动距离
            if initial_pos:
                dx = final_pos[0] - initial_pos[0]
                dy = final_pos[1] - initial_pos[1]
                dz = final_pos[2] - initial_pos[2]
                distance = (dx**2 + dy**2 + dz**2)**0.5
                print(f"实际移动距离: {distance:.3f}mm (ΔX={dx:.3f}, ΔY={dy:.3f}, ΔZ={dz:.3f})")

            return True

        return True

    except Exception as e:
        print(f"移动操作失败: {e}")
        return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
    
    try:
        # 获取当前伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        print(f"当前伺服状态: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
        
        if servo_status == 3:  # 已经是运行状态
            print("✅ 机器人已处于运行状态")
            return True
        
        # 需要上电
        print("🔋 机器人需要上电，开始上电流程...")
        
        # 清除错误
        print("步骤 1: 清除错误...")
        nrc.clear_error(socket_fd)
        time.sleep(0.5)
        
        # 根据当前状态执行相应操作
        if servo_status == 0:  # 停止状态
            print("步骤 2: 设置为就绪状态...")
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.5)
        
        # 执行上电
        print("步骤 3: 执行上电操作...")
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}")
            print("请检查安全回路、示教器模式、急停按钮等")
            return False
        
        time.sleep(2)  # 等待上电完成
        
        # 验证上电结果
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
            return False
            
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\n🔋 开始机器人下电...")
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
    
    try:
        # 执行下电
        result = nrc.set_servo_poweroff(socket_fd)
        if result != 0:
            print(f"❌ 下电命令发送失败！返回码: {result}")
            return False
        
        time.sleep(2)  # 等待下电完成
        
        # 验证下电结果
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 0:
            print("✅ 机器人下电成功！")
            return True
        else:
            print(f"⚠️ 下电后状态: {servo_status} ({servo_names.get(servo_status, '未知状态')})")
            return True  # 某些状态下仍然算作成功
            
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def user_coordinate_movement_test():
    """用户坐标系移动测试主函数"""
    print("=" * 60)
    print("INEXBOT机械臂用户坐标系1操作测试")
    print("=" * 60)

    socket_fd = -1
    user_coord_num = 1  # 用户坐标系1

    try:
        # 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))

        if socket_fd <= 0:
            print("❌ 连接失败！")
            return False

        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 检查并上电机器人
        print("\n🔍 检查机器人状态并上电...")
        if not robot_power_on_if_needed(socket_fd):
            return False

        print("✅ 机器人状态正常，可以执行移动")

        # 步骤1: 检查坐标系状态
        print("\n" + "=" * 40)
        print("步骤1: 检查坐标系状态")
        print("=" * 40)

        current_coord, current_user_num = check_coordinate_system_status(socket_fd)

        # 获取用户坐标系1的参数
        print(f"\n获取用户坐标系{user_coord_num}参数...")
        user_coord_params = get_user_coord_parameters(socket_fd, user_coord_num)
        if user_coord_params is None:
            print("❌ 无法获取用户坐标系1参数")
            return False

        print(f"用户坐标系{user_coord_num}参数:")
        print(f"  原点位置: X={user_coord_params[0]:.3f}, Y={user_coord_params[1]:.3f}, Z={user_coord_params[2]:.3f}")
        print(f"  原点姿态: RX={user_coord_params[3]:.3f}, RY={user_coord_params[4]:.3f}, RZ={user_coord_params[5]:.3f}")

        # 获取当前位置
        current_pos = get_current_cartesian_position(socket_fd)
        if current_pos is None:
            print("❌ 无法获取当前位置")
            return False

        print(f"当前机器人位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
        print(f"当前机器人姿态: RX={current_pos[3]:.3f}, RY={current_pos[4]:.3f}, RZ={current_pos[5]:.3f}")

        # 检查机器人运行状态
        print("🔍 检查机器人运行状态...")
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            running_status = result[1]

        print(f"机器人运行状态: {running_status}")

        # 步骤2: 移动到用户坐标系1中的相对位置 (0, 0, 20) - 即原点上方20mm
        print("\n" + "=" * 40)
        print("步骤2: 移动到用户坐标系1中的位置 (0, 0, 20)")
        print("=" * 40)

        # 在用户坐标系1中，目标位置为 (0, 0, 20) - 即原点上方20mm
        # 保持当前姿态
        target_pos_1 = [0.0, 0.0, 0.0, current_pos[3], current_pos[4], current_pos[5]]

        print(f"用户坐标系1中的目标位置: X={target_pos_1[0]:.3f}, Y={target_pos_1[1]:.3f}, Z={target_pos_1[2]:.3f}")

        success = move_to_position_with_user_coord(
            socket_fd, target_pos_1, user_coord_num,
            "移动到用户坐标系1中的位置 (0, 0, 20)"
        )

        if not success:
            print("❌ 第一次移动失败")
            return False

        print("✅ 第一次移动完成")

        # 步骤3: 执行相对移动到 (10, 10, 25)
        print("\n" + "=" * 40)
        print("步骤3: 执行相对移动到用户坐标系1中的位置 (10, 10, 25)")
        print("=" * 40)

        # 在用户坐标系1中，目标位置为 (10, 10, 25)
        # 相对于上一个位置：X+10mm, Y+10mm, Z+5mm
        target_pos_2 = [10.0, 10.0, 25.0, current_pos[3], current_pos[4], current_pos[5]]

        print(f"用户坐标系1中的目标位置: X={target_pos_2[0]:.3f}, Y={target_pos_2[1]:.3f}, Z={target_pos_2[2]:.3f}")

        success = move_to_position_with_user_coord(
            socket_fd, target_pos_2, user_coord_num,
            "移动到用户坐标系1中的位置 (10, 10, 25)"
        )

        if not success:
            print("❌ 第二次移动失败")
            return False

        print("✅ 第二次移动完成")

        # 步骤4: 返回到用户坐标系1原点上方
        print("\n" + "=" * 40)
        print("步骤4: 返回到用户坐标系1原点上方 (0, 0, 20)")
        print("=" * 40)

        success = move_to_position_with_user_coord(
            socket_fd, target_pos_1, user_coord_num,
            "返回到用户坐标系1原点上方"
        )

        if not success:
            print("❌ 返回移动失败")
            return False

        print("✅ 返回移动完成")

        # 运动完成后执行下电
        robot_power_off(socket_fd)

        print("\n✅ 用户坐标系1操作测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

    finally:
        # 断开连接
        if socket_fd > 0:
            print("\n🔌 正在断开连接...")
            try:
                result = nrc.disconnect_robot(socket_fd)
                print(f"断开连接结果: {result}")
                print("✅ 连接已断开")
            except Exception as e:
                print(f"断开连接时发生错误: {e}")

def main():
    """主函数"""
    print("🚀 开始用户坐标系1操作测试...\n")

    success = user_coordinate_movement_test()

    if success:
        print("\n" + "=" * 60)
        print("🎉 测试完成！")
        print("✅ 成功获取用户坐标系1参数")
        print("✅ 成功移动到用户坐标系1中的位置 (0, 0, 20)")
        print("✅ 成功执行相对移动到位置 (10, 10, 25)")
        print("✅ 成功返回到原点上方位置 (0, 0, 20)")
        print("✅ 所有操作均在用户坐标系1中执行")
        print("✅ 使用了运动状态监控确保移动完成")
    else:
        print("\n" + "=" * 60)
        print("❌ 测试失败")
        print("请检查:")
        print("1. 机械臂是否已开机并正常运行")
        print("2. 用户坐标系1是否已正确设置")
        print("3. 网络连接是否正常")
        print("4. 机械臂是否处于安全位置")
        print("5. 机械臂是否在远程模式下运行")

    print("=" * 60)

if __name__ == "__main__":
    main()
